@php
    use App\Models\User;
@endphp
<div>
    <x-form.input.text label="Phone" labelRequired="0" model="phone" type="tel" placeholder="Enter phone number" />
    <x-form.input.text label="Fax" labelRequired="0" model="fax" type="tel" placeholder="Enter fax number" />
    <x-form.input.drop-down label="Dispatch Method" labelRequired="1" id="dispatch_method" model="dispatch_method">
        <option value="">Select Method</option>
        <option value="{{ User::DISPATCH_METHOD_FAX }}"
            {{ $dispatch_method == User::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
            Fax Plus
        </option>
        <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}"
            {{ $dispatch_method == User::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
            Dispense Pro</option>
    </x-form.input.drop-down>
    @if ($dispatch_method === User::DISPATCH_METHOD_DISPENSE_PRO)
        <x-form.input.text label="DispensePro abbreviation" labelRequired="1" model="dispense_abbreviation" />
    @endif
    <x-form.input.text label="Address" labelRequired="1" model="address" placeholder="Enter street address" />
    <x-form.input.text label="City" labelRequired="1" model="city" placeholder="Enter city" />
    <x-form.input.drop-down label="State" labelRequired="1" model="state" id="state" placeholder="Select State">
        <option value="">Select State</option>
        @foreach ($states as $stateOption)
            <option value="{{ $stateOption->abbreviation }}"
                {{ $state == $stateOption->abbreviation ? 'selected' : '' }}>
                {{ $stateOption->name }}
            </option>
        @endforeach
    </x-form.input.drop-down>
    <x-form.input.text label="ZIP Code" labelRequired="1" model="zip"
        placeholder="Enter ZIP code (12345 or 12345-6789)" />
</div>
@push('scripts')
    <script>
        $(document).ready(function() {
            $('#state').select2({
                width: '100%'
            });
            $('#dispatch_method').select2({
                width: '100%'
            });
        });
    </script>
@endpush
